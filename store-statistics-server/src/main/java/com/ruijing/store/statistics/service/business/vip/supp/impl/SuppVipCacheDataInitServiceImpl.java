package com.ruijing.store.statistics.service.business.vip.supp.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.ruijing.fundamental.api.remote.RemoteResponse;
import com.ruijing.fundamental.cat.Cat;
import com.ruijing.fundamental.common.collections.New;
import com.ruijing.fundamental.common.date.DateUtils;
import com.ruijing.fundamental.common.executor.AsyncExecutor;
import com.ruijing.fundamental.concurrent.ListenableThreadPoolExecutor;
import com.ruijing.fundamental.lang.Preconditions;
import com.ruijing.msharp.clickhouse.client.rpc.ClickhouseRpcClient;
import com.ruijing.order.exception.BusinessInterceptException;
import com.ruijing.shop.category.api.dto.CategoryDTO;
import com.ruijing.shop.crm.api.support.enums.MembershipNameEnums;
import com.ruijing.store.statistics.client.supplier.SuppClient;
import com.ruijing.store.statistics.enums.CommonTimeIntervalEnum;
import com.ruijing.store.statistics.enums.SuppRelatedCacheBizTypeEnum;
import com.ruijing.store.statistics.mapper.SuppBiggestBuyerMapper;
import com.ruijing.store.statistics.mapper.SuppCategoryStatRankMapper;
import com.ruijing.store.statistics.model.SuppBiggestBuyerDO;
import com.ruijing.store.statistics.model.SuppCategoryStatRankDO;
import com.ruijing.store.statistics.service.business.vip.supp.SuppVipCacheDataInitService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @CreateTime 2024-07-16 10:46
 * @Description
 */
@Service
public class SuppVipCacheDataInitServiceImpl implements SuppVipCacheDataInitService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private SuppCategoryStatRankMapper suppCategoryStatRankMapper;

    @Resource
    private ClickhouseRpcClient clickhouseRpcClient;

    @Resource
    private SuppClient suppClient;

    @Resource
    private SuppBiggestBuyerMapper suppBiggestBuyerMapper;

    @Resource(name="dataInitExecutor")
    private ListenableThreadPoolExecutor dataInitExecutor;

    private final Cache<String, Object> localCache = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(64)
            .build();


    @Override
    public void clearExpireCache(Date startTime, Date endTime) {
        suppCategoryStatRankMapper.clearByTime(startTime, endTime);
    }

    @Override
    public void initEnterpriseRankCache(CommonTimeIntervalEnum timeIntervalEnum, boolean needInitPreviousPeriod) {
        List<Integer> suppIdsWithVip = this.getSuppIdWithVip();
        this.initEnterpriseRankCache(suppIdsWithVip, timeIntervalEnum, needInitPreviousPeriod);
    }

    @Override
    public void initEnterpriseRankCache(List<Integer> suppIds, boolean needInitPreviousPeriod) {
        this.initEnterpriseRankCache(suppIds, CommonTimeIntervalEnum.LAST_WEEK, needInitPreviousPeriod);
        this.initEnterpriseRankCache(suppIds, CommonTimeIntervalEnum.LAST_MOUTH, needInitPreviousPeriod);
    }

    private void initEnterpriseRankCache(List<Integer> suppIds, CommonTimeIntervalEnum timeIntervalEnum, boolean needInitPreviousPeriod){
        AsyncExecutor.listenableRunAsync(()->{
            // 1.获取所有一级分类
            Map<Integer, String> firstCategoryMap = suppClient.getCategoryMapping(1);
            Pair<Long, Long> dateRange = timeIntervalEnum.getDateRange();
            Date startDate = new Date(dateRange.getLeft());
            Date endDate = new Date(dateRange.getRight());

            for(Integer categoryId : firstCategoryMap.keySet()){
                // 2.对所有一级分类下的商家，初始化排名
                this.initEnterpriseRank(suppIds, categoryId, startDate, endDate);
            }
            if(needInitPreviousPeriod){
                LocalDateTime lastPeriodStartDateTime = LocalDateTime.ofInstant(startDate.toInstant(), ZoneId.systemDefault());
                Pair<Long, Long> lastPeriodRange = timeIntervalEnum.getDateRangeOnGivenDate(lastPeriodStartDateTime);
                Date lastPeriodStartDate = new Date(lastPeriodRange.getLeft());
                Date lastPeriodDate = new Date(lastPeriodRange.getRight());

                for(Integer categoryId : firstCategoryMap.keySet()){
                    this.initEnterpriseRank(suppIds, categoryId, lastPeriodStartDate, lastPeriodDate);
                }
            }
        }, dataInitExecutor).addFailureCallback((throwable -> {
            logger.error("初始化行业排名失败", throwable);
            Cat.logError("初始化行业排名失败", throwable);
        }));
    }

    @Override
    public void initMarketInsightCache(CommonTimeIntervalEnum timeIntervalEnum) {
        List<Integer> suppIds = getSuppIdWithVip();
        this.initMarketInsightCache(timeIntervalEnum, suppIds);
    }

    @Override
    public void initMarketInsightCache(List<Integer> suppIds) {
        this.initMarketInsightCache(CommonTimeIntervalEnum.LAST_WEEK, suppIds);
        this.initMarketInsightCache(CommonTimeIntervalEnum.LAST_MOUTH, suppIds);
    }

    @Override
    public void initDemandInsightCache(List<Integer> suppIds) {
        if(CollectionUtils.isEmpty(suppIds)){
            suppIds = getSuppIdWithVip();
        }
        LocalDateTime endDateTime = LocalDateTime.now(ZoneId.systemDefault());
        LocalDateTime startDateTime = endDateTime.minusMonths(3);
        Date endDate = new Date(endDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        Date startDate = new Date(startDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        suppIds.forEach(suppId->this.initSuppBiggest1000Buyer(suppId, startDate, endDate));
    }

    private void initMarketInsightCache(CommonTimeIntervalEnum timeIntervalEnum, List<Integer> suppIds){
        AsyncExecutor.listenableRunAsync(()->{
            Map<Integer, String> firstCategoryMap = suppClient.getCategoryMapping(1);
            Map<Integer, String> secondCategoryMap = suppClient.getCategoryMapping(2);
            Pair<Long, Long> dateRange = timeIntervalEnum.getDateRange();
            Date startDate = new Date(dateRange.getLeft());
            Date endDate = new Date(dateRange.getRight());
            for(Integer categoryId : firstCategoryMap.keySet()){
                // 对于每个一级分类，进行一次初始化
                this.initMarketInsightByTime(categoryId, startDate, endDate, suppIds);
            }
            for (Integer categoryId : secondCategoryMap.keySet()) {
                // 对于每个二级分类，进行一次初始化
                this.initMarketInsightByTime(categoryId, startDate, endDate, suppIds);
            }
            // 全部分类再来一遍（即用户未选择分类时的统计）
            this.initMarketInsightByTime(null, startDate, endDate, suppIds);
        }, dataInitExecutor).addFailureCallback((throwable -> {
            logger.error("初始化市场洞察失败", throwable);
            Cat.logError("初始化市场洞察失败", throwable);
        }));
    }

    private List<Integer> getSuppIdWithVip(){
        return suppClient.queryVipSuppIdByMemberCode(MembershipNameEnums.DATA_UPGRADES.name());
    }

    private void initMarketInsightByTime(Integer categoryId, Date startDate, Date endDate, List<Integer> suppIds){
        // 获取单位-总交易量映射。即获取所有单位的总交易额
        Map<Integer, BigDecimal> lastWeekOrgTotalAmountMap = this.getOrgTotalAmountMap(categoryId, startDate, endDate);
        // 初始化优势单位/潜力单位
        this.initBestAndWorstSellOrgForSupp(categoryId, startDate, endDate, suppIds, lastWeekOrgTotalAmountMap);
        for(Map.Entry<Integer, BigDecimal> entry : lastWeekOrgTotalAmountMap.entrySet()){
            // 对每个单位遍历
            if(BigDecimal.ZERO.compareTo(entry.getValue()) == 0){
                continue;
            }
            Integer orgId = entry.getKey();
            // 初始化市场洞察单位列表，获取进入交易额在此单位排名TOP100的单位
            this.initListForBestSellSuppInOrg(orgId, categoryId, startDate, endDate, suppIds);
        }
    }

    /**
     * 初始化优势单位/潜力单位
     * @param categoryId 分类
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param suppIds 供应商id
     */
    private void initBestAndWorstSellOrgForSupp(Integer categoryId, Date startDate, Date endDate, List<Integer> suppIds, Map<Integer, BigDecimal> orgTotalAmountMap){
        List<SuppCategoryStatRankDO> dataList = New.list();
        for(Integer suppId : suppIds) {
            // 对每个SVIP商家独立计算

            // 获取供应商在单位的交易量。
            Map<Integer, BigDecimal> suppTotalAmountInOrgMap = this.getSuppAmountInOrg(suppId, categoryId, startDate, endDate);
            if(suppTotalAmountInOrgMap.isEmpty()){
                continue;
            }
            Map<Integer, BigDecimal> suppRatioInOrgMap = New.mapWithCapacity(suppTotalAmountInOrgMap.size());
            // 计算每个供应商在单位的交易量占单位交易额的比重
            suppTotalAmountInOrgMap.forEach((k, v) -> {
                BigDecimal orgAmount = orgTotalAmountMap.get(k);
                if(BigDecimal.ZERO.compareTo(orgAmount) == 0){
                    return;
                }
                BigDecimal ratio = v.divide(orgAmount, 4, RoundingMode.HALF_UP);
                suppRatioInOrgMap.put(k, ratio);
            });
            if(suppRatioInOrgMap.isEmpty()){
                continue;
            }
            List<Map.Entry<Integer, BigDecimal>> orgIdEntryOrderByRatio = suppRatioInOrgMap.entrySet().stream()
                    .sorted((e1, e2) -> {
                        // 先按占比降序
                        int compareResult = e2.getValue().compareTo(e1.getValue());
                        if (compareResult == 0) {
                            // 占比相同时，按交易额降序
                            return suppTotalAmountInOrgMap.get(e2.getKey())
                                    .compareTo(suppTotalAmountInOrgMap.get(e1.getKey()));
                        }
                        return compareResult;
                    })
                    .collect(Collectors.toList());
            // 1.获取优势单位：先获取占比最多的，如果有多个相同占比取金额最大的
            Map.Entry<Integer, BigDecimal> bestSellOrgRatio = orgIdEntryOrderByRatio.get(0);
            BigDecimal maxAmount = suppTotalAmountInOrgMap.get(bestSellOrgRatio.getKey());

            SuppCategoryStatRankDO bestSellRank = new SuppCategoryStatRankDO();
            bestSellRank.setSuppId(suppId);
            bestSellRank.setOrgId(bestSellOrgRatio.getKey());
            bestSellRank.setStartDate(startDate);
            bestSellRank.setEndDate(endDate);
            bestSellRank.setCategoryId(categoryId);
            bestSellRank.setAmount(maxAmount);
            bestSellRank.setAmountRatio(maxAmount.divide(orgTotalAmountMap.get(bestSellOrgRatio.getKey()), 4, RoundingMode.HALF_UP).movePointRight(2));
            bestSellRank.setBusinessType(SuppRelatedCacheBizTypeEnum.SUPP_BEST_AMOUNT_RATIO_ORG.getCode());
            bestSellRank.setRanking(((Long) this.qrySuppRankInOrg(suppId, bestSellOrgRatio.getKey(), categoryId, startDate, endDate).get(0).get("rank")).intValue());
            dataList.add(bestSellRank);

            List<Integer> orgIdOrderByRatio = orgIdEntryOrderByRatio.stream().map(Map.Entry::getKey).collect(Collectors.toList());
            if (orgIdEntryOrderByRatio.size() > 1) {
                // 若不止一个单位，则第一名往后的倒数两个是潜力单位
                List<Integer> worstSellOrg = orgIdOrderByRatio.subList(Math.max(1, orgIdEntryOrderByRatio.size() - 2), orgIdEntryOrderByRatio.size());
                for (Integer worstSellOrgId : worstSellOrg) {
                    SuppCategoryStatRankDO worstSellOrgRank = new SuppCategoryStatRankDO();
                    worstSellOrgRank.setSuppId(suppId);
                    worstSellOrgRank.setOrgId(worstSellOrgId);
                    worstSellOrgRank.setStartDate(startDate);
                    worstSellOrgRank.setEndDate(endDate);
                    worstSellOrgRank.setCategoryId(categoryId);
                    worstSellOrgRank.setAmount(suppTotalAmountInOrgMap.get(worstSellOrgId));
                    worstSellOrgRank.setAmountRatio(suppTotalAmountInOrgMap.get(worstSellOrgId).divide(orgTotalAmountMap.get(worstSellOrgId), 4, RoundingMode.HALF_UP).movePointRight(2));
                    worstSellOrgRank.setBusinessType(SuppRelatedCacheBizTypeEnum.SUPP_WORST_AMOUNT_RATIO_ORG.getCode());
                    worstSellOrgRank.setRanking(((Long) this.qrySuppRankInOrg(suppId, worstSellOrgId, categoryId, startDate, endDate).get(0).get("rank")).intValue());
                    dataList.add(worstSellOrgRank);
                }
            }
        }
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        suppCategoryStatRankMapper.insertList(dataList);
    }

    /**
     * 获取企业排名
     *
     * @param suppIds 需要初始的suppIds
     */
    private void initEnterpriseRank(List<Integer> suppIds, int categoryId, Date startDate, Date endDate){
        String selectSql = "SELECT supp_id, sum(bid_amount) AS sum_res FROM `order`.order_stat final ";
        String whereSql = "WHERE order_status NOT IN (3, 30) AND species = 0 " +
                "AND order_date > ? AND order_date  < ? AND first_category_id = ? ";
        String groupBySql = "GROUP BY supp_id";
        List<Object> params;
        params = New.list(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startDate),
                DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endDate),
                categoryId);
        String sql = "SELECT * FROM (" + selectSql + whereSql + groupBySql + ") ORDER BY sum_res DESC";
        List<Map<String, Object>> ckData = this.queryClickhouse(sql, params);
        List<SuppCategoryStatRankDO> dataList = New.listWithCapacity(ckData.size());
        Map<String, Object> preItem = null;
        for(int i = 0; i < ckData.size(); i++){
            Map<String, Object> item = ckData.get(i);
            BigDecimal amount = new BigDecimal(item.get("sum_res").toString());
            if(BigDecimal.ZERO.compareTo(amount) == 0){
                preItem = null;
                continue;
            }
            if(i < 5){
                // 当前供应商属于前五名，需要初始化，则写入
                dataList.add(this.parseSuppRelatedMap2CacheItem(item, categoryId, startDate, endDate, i));
            } else if(suppIds.contains(((Long) item.get("supp_id")).intValue())){
                // 当前供应商属于VIP供应商，需要初始化，则写入
                dataList.add(this.parseSuppRelatedMap2CacheItem(item, categoryId, startDate, endDate, i));
                if(preItem != null){
                    dataList.add(this.parseSuppRelatedMap2CacheItem(preItem, categoryId, startDate, endDate, i - 1));
                }
                // 由于当前供应商被初始化，所以初始化下一名的时候，不用再写入当前供应商的数据
                preItem = null;
            } else {
                // 当前供应商没被初始化，则若下一名需要初始化时，需要初始化当前供应商数据做对比
                preItem = item;
            }
        }
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        suppCategoryStatRankMapper.insertList(dataList);
    }

    private SuppCategoryStatRankDO parseSuppRelatedMap2CacheItem(Map<String, Object> item, int categoryId, Date startDate, Date endDate, int currentIdx){
        BigDecimal amount = new BigDecimal(item.get("sum_res").toString());
        SuppCategoryStatRankDO suppCategoryStatRankDO = new SuppCategoryStatRankDO();
        suppCategoryStatRankDO.setSuppId(((Long) item.get("supp_id")).intValue());
        suppCategoryStatRankDO.setStartDate(startDate);
        suppCategoryStatRankDO.setEndDate(endDate);
        suppCategoryStatRankDO.setCategoryId(categoryId);
        suppCategoryStatRankDO.setAmount(amount);
        suppCategoryStatRankDO.setBusinessType(SuppRelatedCacheBizTypeEnum.VALID_AMOUNT_RANK.getCode());
        suppCategoryStatRankDO.setRanking(currentIdx+1);
        return suppCategoryStatRankDO;
    }

    /**
     * 获取单位金额
     * @param categoryId 分类id
     * @param startDate 时间
     * @param endDate 结束时间
     * @return 单位id-金额映射
     */
    private Map<Integer, BigDecimal> getOrgTotalAmountMap(Integer categoryId, Date startDate, Date endDate){
        String selectSql = "SELECT org_id, sum(bid_amount) AS sum_res FROM `order`.order_stat final ";
        String whereSql = "WHERE order_date > ? AND order_date  < ? AND order_status != 30 AND species = 0 ";
        String groupSql = "GROUP BY org_id ORDER BY SUM(bid_amount) DESC";
        List<Object> params = New.list(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startDate),
                DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endDate));
        if(categoryId != null){
            whereSql += getCategoryWhereSql(categoryId);
            params.add(categoryId);
        }
        String sql = selectSql + whereSql + groupSql;
        List<Map<String, Object>> ckData = queryClickhouse(sql, params);
        if(CollectionUtils.isEmpty(ckData)){
            return New.emptyMap();
        }
        Map<Integer, BigDecimal> resultMap = New.mapWithCapacity(ckData.size());
        for(Map<String, Object> m : ckData){
            BigDecimal amount = new BigDecimal(m.get("sum_res").toString());
            if(BigDecimal.ZERO.compareTo(amount) == 0){
                continue;
            }
            resultMap.put(((Long) m.get("org_id")).intValue(), amount);
        }
        return resultMap;
    }

    private Map<Integer, BigDecimal> getSuppAmountInOrg(int suppId, Integer categoryId, Date startDate, Date endDate){
        String selectSql = "SELECT org_id, sum(bid_amount) AS sum_res FROM `order`.order_stat final ";
        String whereSql = "WHERE supp_id = ? AND order_date > ? AND order_date  < ? AND order_status != 30 AND species = 0 ";
        String groupSql = "GROUP BY org_id ORDER BY SUM(bid_amount) DESC";
        List<Object> params = New.list(suppId,
                DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startDate),
                DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endDate));
        if(categoryId != null){
            whereSql += getCategoryWhereSql(categoryId);
            params.add(categoryId);
        }
        String sql = selectSql + whereSql + groupSql;
        List<Map<String, Object>> ckData = queryClickhouse(sql, params);
        if(CollectionUtils.isEmpty(ckData)){
            return New.emptyMap();
        }
        Map<Integer, BigDecimal> resultMap = New.mapWithCapacity(ckData.size());
        for(Map<String, Object> m : ckData){
            BigDecimal amount = new BigDecimal(m.get("sum_res").toString());
            if(BigDecimal.ZERO.compareTo(amount) == 0){
                continue;
            }
            resultMap.put(((Long) m.get("org_id")).intValue(), amount);
        }
        return resultMap;
    }

    /**
     * 查询在单位里面卖的最好的商家
     * @param orgId 单位id
     * @param categoryId 分类id
     * @param startDate 起始时间
     * @param endDate 截止时间
     */
    private void initListForBestSellSuppInOrg(Integer orgId, Integer categoryId, Date startDate, Date endDate, List<Integer> suppIds){
        // 查询在指定单位里面供应商的交易量排名
        List<Map<String, Object>> ckData = this.qrySuppRankInOrg(null, orgId, categoryId, startDate, endDate);
        if(CollectionUtils.isEmpty(ckData)){
            return;
        }
        List<SuppCategoryStatRankDO> dataList = New.listWithCapacity(ckData.size());
        for(int i = 0; i < ckData.size(); i++){
            Map<String, Object> item = ckData.get(i);
            BigDecimal amount = new BigDecimal(item.get("sum_res").toString());
            if(BigDecimal.ZERO.compareTo(amount) == 0){
                continue;
            }
            int suppId = ((Long) item.get("supp_id")).intValue();
            if(!suppIds.contains(suppId)){
                // 非开通VIP供应商不初始化
                continue;
            }
            SuppCategoryStatRankDO suppCategoryStatRankDO = new SuppCategoryStatRankDO();
            suppCategoryStatRankDO.setSuppId(((Long) item.get("supp_id")).intValue());
            suppCategoryStatRankDO.setOrgId(orgId);
            suppCategoryStatRankDO.setStartDate(startDate);
            suppCategoryStatRankDO.setEndDate(endDate);
            suppCategoryStatRankDO.setCategoryId(categoryId);
            suppCategoryStatRankDO.setAmount(amount);
            suppCategoryStatRankDO.setBusinessType(SuppRelatedCacheBizTypeEnum.SUPP_AMOUNT_IN_ORG.getCode());
            suppCategoryStatRankDO.setRanking(((Long) item.get("rank")).intValue());
            dataList.add(suppCategoryStatRankDO);
        }
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        suppCategoryStatRankMapper.insertList(dataList);
    }

    /**
     * 获取分类Level
     *
     * @param categoryId 分类id
     * @return 分类level 1/2/3
     */
    @SuppressWarnings("unchecked")
    private Integer getCategoryLevel(Integer categoryId) {
        // 先查缓存
        final String cacheKey = "category_level";
        Map<Integer, Integer> categoryId2Level = (Map<Integer, Integer>) localCache.getIfPresent(cacheKey);
        if (MapUtils.isNotEmpty(categoryId2Level)) {
            return categoryId2Level.get(categoryId);
        }
        // 查询1、2级分类信息
        List<CategoryDTO> firstCategoryList = suppClient.queryCategoryByLevel(1);
        List<CategoryDTO> secondCategoryList = suppClient.queryCategoryByLevel(2);
        // 转成 ID-LEVEL map
        Map<Integer, Integer> categoryId2LevelMap = New.mapWithCapacity(firstCategoryList.size() + secondCategoryList.size());
        for (CategoryDTO categoryDTO : firstCategoryList) {
            categoryId2LevelMap.put(categoryDTO.getId().intValue(), categoryDTO.getLevel());
        }
        for (CategoryDTO categoryDTO : secondCategoryList) {
            categoryId2LevelMap.put(categoryDTO.getId().intValue(), categoryDTO.getLevel());
        }
        // 缓存
        localCache.put(cacheKey, categoryId2LevelMap);
        return categoryId2LevelMap.get(categoryId);
    }

    /**
     * 根据分类ID获取拼接的where条件
     */
    private String getCategoryWhereSql(Integer categoryId) {
        if (Objects.isNull(categoryId)) {
            return StringUtils.EMPTY;
        }
        Integer level = getCategoryLevel(categoryId);
        switch (level) {
            case 1:
                return " AND first_category_id = ? ";
            case 2:
                return " AND second_category_id = ? ";
            default:
                throw new BusinessInterceptException(StrUtil.format("暂不支持的分类level,分类Id:{},level:{}", categoryId, level));
        }
    }

    /**
     * 查询供应商在单位的交易额排名
     * @param suppId 供应商id--选填
     * @param orgId 单位id
     * @param categoryId 一级分类id
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return 占比
     */
    private List<Map<String, Object>> qrySuppRankInOrg(Integer suppId, Integer orgId, Integer categoryId, Date startDate, Date endDate){
        String whereSql = "WHERE order_date > ? AND order_date  < ? AND org_id = ? AND order_status != 30 AND species = 0 ";
        List<Object> params = New.list(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startDate),
                DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endDate),
                orgId);
        if(categoryId != null){
            whereSql += getCategoryWhereSql(categoryId);
            params.add(categoryId);
        }
        String outerWhereSql = "";
        if(suppId != null){
            outerWhereSql += " WHERE supp_id = ? ";
            params.add(suppId);
        }
        String sql = "SELECT * FROM (" +
                "SELECT rowNumberInAllBlocks()+1 AS rank,supp_id, sum(bid_amount) AS sum_res FROM `order`.order_stat final " +
                whereSql +
                "GROUP BY supp_id " +
                "ORDER BY SUM(bid_amount) DESC) " +
                outerWhereSql + " LIMIT 100";
        List<Map<String, Object>> ckData = queryClickhouse(sql, params);
        if(CollectionUtils.isEmpty(ckData)){
            return New.emptyList();
        }
        return ckData;
    }

    private void initSuppBiggest1000Buyer(Integer suppId, Date startDate, Date endDate){
        suppBiggestBuyerMapper.deleteBySuppId(suppId);
        String sql = "SELECT * FROM (SELECT buyer_id, sum(bid_amount) AS sum_res FROM `order`.order_stat final " +
                "WHERE species = 0 AND order_date > ? AND order_date  < ? " +
                "AND supp_id = ? AND order_status NOT IN (30) " +
                "GROUP BY buyer_id " +
                "ORDER BY sum_res desc) LIMIT 1000";
        List<Object> params = New.list(DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, startDate),
                DateUtils.format(DateUtils.SIMPLE_DATE_FORMAT, endDate),
                suppId);
        List<Map<String, Object>> ckData = queryClickhouse(sql, params);
        if(CollectionUtils.isEmpty(ckData)){
            return;
        }
        List<SuppBiggestBuyerDO> dataList = New.listWithCapacity(ckData.size());
        for(int i = 0; i < ckData.size(); i++){
            Map<String, Object> item = ckData.get(i);
            SuppBiggestBuyerDO row = new SuppBiggestBuyerDO();
            row.setSuppId(suppId);
            row.setBuyerId(((Long) item.get("buyer_id")).intValue());
            dataList.add(row);
        }
        suppBiggestBuyerMapper.insertList(dataList);
    }

    private List<Map<String, Object>> queryClickhouse(String sql, List<Object> params){
        RemoteResponse<List<Map<String, Object>>> remoteResponse = clickhouseRpcClient.list("order", sql, params.toArray());
        Preconditions.isTrue(remoteResponse.isSuccess(), remoteResponse.getMsg());
        return remoteResponse.getData();
    }
}
